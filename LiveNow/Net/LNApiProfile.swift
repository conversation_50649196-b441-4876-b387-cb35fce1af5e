import UIKit
import Moya


public enum LNApiProfile {
    /// 用户信息
    case userDetail(par: [String: Any])
    /// 编辑用户
    case userUpdate(par: [String: Any])
    /// VIP价格
    case vipPrice(par: [String: Any])
    /// 充值价格
    case rechargePrice(par: [String: Any])
    /// 黑名单列表
    case blackList(par: [String: Any])
    /// 拉黑用户
    case blackUser(par: [String: Any])
    /// 取消拉黑
    case removeBlack(par: [String: Any])
    /// 充值记录
    case rechargeRecords(par: [String: Any])
    /// 充值消费记录
    case diamondRecords(par: [String: Any])
    /// 免打扰设置  ---   先不调用接口，使用本地存储
    case disturbSwitch(par: [String: Any])
}

extension LNApiProfile: Moya.TargetType {
    public var baseURL: URL {
        switch self {
        case .vipPrice, .rechargePrice, .rechargeRecords:
            return URL(string: apiURL + "/ks-order/")!
        default:
            return URL(string: apiURL + "/blade-auth/")!
        }
    }

    public var path: String {
        var baseURL = ""
        var p: [String: Any]?
        switch self {
        case .userDetail(let par):
            baseURL = "user/detail"
            p = par
        case .userUpdate(let par):
            baseURL = "user/update"
            p = par
        case .vipPrice(let par):
            baseURL = "order/price"
            p = par
        case .rechargePrice(let par):
            baseURL = "order/price"
            p = par
        case .blackList(let par):
            baseURL = "user/black/list"
            p = par
        case .blackUser(let par):
            baseURL = "user/black"
            p = par
        case .removeBlack(let par):
            baseURL = "user/black/remove"
            p = par
        case .rechargeRecords(let par):
            baseURL = "order/recharge/records"
            p = par
        case .diamondRecords(let par):
            baseURL = "diamond/record/page"
            p = par
        case .disturbSwitch(let par):
            baseURL = "user/disturb/switch"
            p = par
        }

        if let parDict = p {
            var namedPaird = [String]()
            for(key, value) in parDict {
                if let valueStr = value as? String, let utf8Str = valueStr.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    // 处理url上包含中文时，进行编码
                    namedPaird.append("\(key)=\(utf8Str)")
                } else {
                    namedPaird.append("\(key)=\(value)")
                }
            }
            let signedString = namedPaird.joined(separator:"&")
            return baseURL + "?" + signedString
        } else {
            return baseURL
        }
    }
        
    public var method: Moya.Method {
        switch self {
        case .userDetail:
            return .get
        case .userUpdate, .vipPrice, .rechargePrice, .blackList,  .blackUser, .removeBlack, .disturbSwitch, .rechargeRecords, .diamondRecords:
            return .post
        }
    }
    
    /// 这个是做单元测试模拟的数据，必须要实现，只在单元测试文件中有作用
    public var sampleData: Data {
        return "".data(using: String.Encoding.utf8)!
    }
    
    public var task: Task {
        var params: [String: Any]? = nil

        switch self {
        case .userUpdate(let par),
             .vipPrice(let par),
             .rechargePrice(let par),
             .blackList(let par),
             .rechargeRecords(let par),
             .diamondRecords(let par),
             .blackUser(let par),
             .removeBlack(let par),
             .disturbSwitch(let par):
            params = par
        case .userDetail(_):
            // GET请求的参数已经在path中处理了
            return .requestPlain
        }
        if let params = params {
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
        return .requestPlain
    }
    
    public var headers: [String: String]? {
        
        
        return LNNetApiTool.networkHeaders()
    }
}
