//
//  LNFollowView.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import SnapKit
import JKSwiftExtension
import JXSegmentedView

/// Follow 页面视图 - 展示关注的直播内容
class LNFollowView: UIView {
    
    // MARK: - UI Elements
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = s(15)
        layout.sectionInset = UIEdgeInsets(top: s(10), left: s(15), bottom: s(10), right: s(15))
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNLiveStreamCell.self, forCellWithReuseIdentifier: "LNLiveStreamCell")
        return collectionView
    }()
    
    // MARK: - Data
    private var liveStreams: [LNLiveStreamModel] = []
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        loadData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        backgroundColor = .clear
        addSubview(collectionView)
    }
    
    private func setupConstraints() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func loadData() {
        // 模拟数据 - 关注的用户数据
        liveStreams = [
            LNLiveStreamModel(
                id: "11",
                username: "NIKENAME",
                status: .online,
                countryFlag: "🇵🇸",
                pricePerMinute: "60/min",
                avatarImageName: "avatar11",
                hasVideoCall: true,
                hasGift: true
            ),
            LNLiveStreamModel(
                id: "12",
                username: "NIKENAME",
                status: .offline,
                countryFlag: "🇯🇴",
                pricePerMinute: "60/min",
                avatarImageName: "avatar12",
                hasVideoCall: false,
                hasGift: false
            ),
            LNLiveStreamModel(
                id: "13",
                username: "NIKENAME",
                status: .busy,
                countryFlag: "🇵🇸",
                pricePerMinute: "60/min",
                avatarImageName: "avatar13",
                hasVideoCall: true,
                hasGift: false
            )
        ]
        
        collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDataSource
extension LNFollowView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return liveStreams.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNLiveStreamCell", for: indexPath) as! LNLiveStreamCell
        cell.configure(with: liveStreams[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNFollowView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (kscreenW - s(45)) / 2
        let height = s(280)
        return CGSize(width: width, height: height)
    }
}

// MARK: - UICollectionViewDelegate
extension LNFollowView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let liveStream = liveStreams[indexPath.item]
        print("选中了直播间: \(liveStream.username)")
        // TODO: 跳转到直播间详情页
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension LNFollowView: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return self
    }
}
