//
//  LNLiveStreamModel.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import Foundation
import UIKit

/// 用户状态枚举
enum LNUserStatus {
    case online
    case offline
    case busy

    var displayText: String {
        switch self {
        case .online:
            return "Online"
        case .offline:
            return "Offline"
        case .busy:
            return "Busy"
        }
    }

    var dotColor: UIColor {
        switch self {
        case .online:
            return UIColor.hex(hexString: "#00FF91")
        case .offline:
            return UIColor.hex(hexString: "#DADADA")
        case .busy:
            return UIColor.hex(hexString: "#FF1500")
        }
    }

    var textColor: UIColor {
        switch self {
        case .online, .offline:
            return .white
        case .busy:
            return UIColor.hex(hexString: "#FF1500")
        }
    }
}

/// 直播流数据模型
struct LNLiveStreamModel {
    /// 用户ID
    let id: String

    /// 用户名
    let username: String

    /// 用户状态
    let status: LNUserStatus
    
    /// 国家旗帜
    let countryFlag: String
    
    /// 每分钟价格
    let pricePerMinute: String
    
    /// 头像图片名称
    let avatarImageName: String
    
    /// 是否支持视频通话
    let hasVideoCall: Bool
    
    /// 是否有礼物
    let hasGift: Bool
}
