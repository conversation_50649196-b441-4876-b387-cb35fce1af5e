//
//  LNWhoLikedMeCardView.swift
//  LiveNow
//
//  Created by po on 2025/8/17.
//

import UIKit

class LNWhoLikedMeCardView: UIView {
    
    private lazy var bgView = UIView()
    private lazy var lockIcon = UIImageView()
    private lazy var label = UILabel()
    private lazy var avatarStackView = LNMessageAvatarStack()
    private lazy var countLabel = UILabel()
    private lazy var countBadge = UIView()
    private lazy var chevronIcon = UIImageView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        bgView.jk.gradientColor(.horizontal, [UIColor.hex(hexString: "#AFFFE3").cgColor, UIColor.hex(hexString: "#B7FDFF").cgColor], [0.0, 1.0])
    }
    func setupUI() {
        addSubview(bgView)
        addSubview(lockIcon)
        addSubview(label)
        addSubview(avatarStackView)
        addSubview(countBadge)
        countBadge.addSubview(countLabel)
        addSubview(chevronIcon)
        
        lockIcon.image =  UIImage(named: "ic_msg_lock")
        label.text = "who liked me"
        label.textColor = UIColor.hex(hexString: "#00DFAB")
        label.font = LNFont.medium(18)
        
        countLabel.text = "10"
        countLabel.textColor = .white
        countLabel.font = LNFont.bold(14)
        countLabel.textAlignment = .center
        
        countBadge.backgroundColor = UIColor.hex(hexString: "#00DFAB")
        countBadge.layer.cornerRadius = 12
        
        chevronIcon.image = UIImage(named: "ic_msg_arrow")
        
        bgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        lockIcon.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        label.snp.makeConstraints { make in
            make.left.equalTo(lockIcon.snp.right).offset(8)
            make.centerY.equalToSuperview()
        }
        
        avatarStackView.snp.makeConstraints { make in
            make.right.equalTo(countBadge.snp.left).offset(-12)
            make.centerY.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(90)
        }
        
        countBadge.snp.makeConstraints { make in
            make.right.equalTo(chevronIcon.snp.left).offset(-8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        countLabel.snp.makeConstraints { make in
            make.edges.equalTo(countBadge)
        }
        
        chevronIcon.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(16)
            make.height.equalTo(16)
        }
    }
    
    /*
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    override func draw(_ rect: CGRect) {
        // Drawing code
    }
    */

}


class LNMessageAvatarStack: UIView {
    
}
