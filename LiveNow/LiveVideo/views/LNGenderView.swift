//
//  LNGenderView.swift
//  LiveNow
//
//  Created by pp on 2025/8/17.
//

import UIKit
import JKSwiftExtension

class LNGenderView: UIView {
    
    private var gender: Int = 0 {
        didSet {
            genderIcon.image = UIImage(named: "")
            backgroundColor("#FF77C9")
        }
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor("#FF77C9").corner(s(7))
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        addSubview(genderIcon)
        addSubview(ageLabel)
        genderIcon.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(s(4))
            make.size.equalTo(s(12))
        }
        ageLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(genderIcon.snp.trailing).offset(s(2))
            make.height.equalTo(s(10))
            make.trailing.equalTo(s(-6))
        }
    }
    
    func bindModel(_ user: LNUserModel) {
        ageLabel.text("\(user.age)")
//        gender = user.gender
    }

    // MARK: - Lazy
    lazy var genderIcon: UIImageView = {
        let view = UIImageView()
        view.backgroundColor(.clear)
        view.image = UIImage(named: "ic_gender_female")
        return view
    }()
    
    lazy var ageLabel: UILabel = {
        let label = UILabel()
        label.font(LNFont.regular(10)).color("#FFFFFF").text("0")
        return label
    }()
}
