//
//  LNVideoChatVC+Delegate.swift
//  LiveNow
//
//  Created by edy on 2025/8/11.
//

extension LNVideoChatVC: LNVideoBottomToolViewDelegate {
    func bottomGiftItemAction() {
        giftContainerView.showModel(LNGiftChannelModel("", "", 1, "", 1, "", "", 1, 1))
    }
    
    func bottomMicItemAction() {
        
    }
    
    func bottomBlurItemAction() {
        
    }
    
    func bottomChatItemAction() {
        self.chatInput.textInput.becomeFirstResponder()
    }
    
    
    
    
}
