//
//  AppDelegate.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?
    static let shared = UIApplication.shared.delegate as! AppDelegate

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        setUps()
        
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.backgroundColor = .white
        if #available(iOS 13.0, *) {
            self.window?.overrideUserInterfaceStyle = .light
        }
        resetTabVC()
        
        return true
    }

    func resetTabVC() {
        if LNUserManager.shared.isLogin() {
            
            let rootVc = self.window?.rootViewController
            let vc = LNTabBarController()
//            let nav = UINavigationController(rootViewController: vc)
            self.window?.rootViewController = vc
//            self.startTransitionAnimation(old: rootVc, new: nav)
            
        } else {
            let login = LNLoginViewController()
            let nav = LNNavigationViewController(rootViewController: login)
            window?.rootViewController = nav
        }
        
        // 显示窗口
        window?.makeKeyAndVisible()
    }
    
    private func setUps() {
        // 全局字体接入
        LNFontManager.shared.activate()
        // Override point for customization after application launch.
        LNAgoraManager.shared.initRtcEngine()
        LNUserManager.shared.loadUserInfo()
    }

    private func startTransitionAnimation(old: UIViewController?, new: UIViewController?) {
        guard let old = old, let new = new else { return }
        let duration = TimeInterval(UINavigationController.hideShowBarDuration)
        UIView.transition(from: old.view, to: new.view, duration: duration, options: .transitionCrossDissolve)
    }
}

