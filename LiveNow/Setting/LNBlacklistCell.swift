//
//  LNBlacklistCell.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/08/13.
//

import UIKit
import SnapKit
import Kingfisher

final class LNBlacklistCell: UITableViewCell {
    static let reuseId = "LNBlacklistCell"

    var onRemove: (() -> Void)?

    private lazy var avatarView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.layer.cornerRadius = s(24)
        iv.backgroundColor = UIColor.systemGray5
        return iv
    }()

    private lazy var nameLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.forTextStyle(.headline)
        l.textColor = UIColor.label
        l.adjustsFontForContentSizeCategory = true
        return l
    }()

    private lazy var ageBadge: UILabel = Self.makeBadge(bg: UIColor.hex(hexString: "#FF77C9"), textColor: .white)
    private lazy var idBadge: UILabel = Self.makeBadge(bg: UIColor.hex(hexString: "#44E3D0"), textColor: .white)

    private lazy var removeButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("Remove", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = LNFont.forTextStyle(.subheadline)
        b.contentEdgeInsets = UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16)
        b.backgroundColor = UIColor.hex(hexString: "#93DFBC")
        b.layer.cornerRadius = s(18)
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(removeTapped), for: .touchUpInside)
        return b
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        contentView.addSubview(avatarView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(ageBadge)
        contentView.addSubview(idBadge)
        contentView.addSubview(removeButton)

        avatarView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(48))
        }
        nameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(12))
            make.left.equalTo(avatarView.snp.right).offset(s(12))
            make.right.lessThanOrEqualTo(removeButton.snp.left).offset(-s(8))
        }
        ageBadge.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(s(8))
            make.height.equalTo(s(20))
        }
        idBadge.snp.makeConstraints { make in
            make.left.equalTo(ageBadge.snp.right).offset(s(8))
            make.centerY.equalTo(ageBadge)
            make.height.equalTo(s(20))
            make.right.lessThanOrEqualTo(removeButton.snp.left).offset(-s(12))
        }
        removeButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-s(16))
        }
    }

    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    func configure(with item: LNBlockUser) {
        nameLabel.text = item.name
        ageBadge.text = "  \(item.ageTag)  "
        idBadge.text = "  \(item.idTag)  "

        // 使用Kingfisher加载头像
        if let avatarURLString = item.avatarURL, !avatarURLString.isEmpty {
            let url = URL(string: avatarURLString)
            avatarView.kf.setImage(
                with: url,
                placeholder: UIImage(systemName: "person.crop.circle"),
                options: [
                    .transition(.fade(0.3)),
                    .cacheOriginalImage
                ]
            )
        } else {
            // 使用默认头像
            avatarView.image = UIImage(systemName: "person.crop.circle")
        }
    }

    @objc private func removeTapped() { onRemove?() }

    private static func makeBadge(bg: UIColor, textColor: UIColor) -> UILabel {
        let l = UILabel()
        l.font = LNFont.forTextStyle(.caption1)
        l.textColor = textColor
        l.backgroundColor = bg
        l.layer.cornerRadius = s(10)
        l.layer.masksToBounds = true
        l.setContentHuggingPriority(.required, for: .horizontal)
        l.setContentCompressionResistancePriority(.required, for: .horizontal)
        return l
    }
}


