//
//  LNBlacklistViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit
import MJRefresh
import HandyJSON

/// 黑名单页面
/// 设计依据：提供的截图。头像 + 名称 + 两个徽标（年龄/ID）+ 右侧绿色 "Remove" 胶囊按钮
/// 底部展示 "No More Data"。
class LNBlacklistViewController: LNBaseController, UITableViewDataSource, UITableViewDelegate {

        // 使用白色导航栏
    override var navigationSolidColor: UIColor { .white }
    // MARK: - Data
    private var data: [LNBlacklistUser] = []

    // 与 LNDiamondHistoryViewController 保持一致的分页/刷新配置
    private var current: Int = 1
    private let size: Int = 10
    private var hasMore: Bool = true

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.dataSource = self
        tv.delegate = self
        tv.separatorStyle = .none
        tv.backgroundColor = .white
        tv.register(LNBlacklistCell.self, forCellReuseIdentifier: LNBlacklistCell.reuseId)
        tv.rowHeight = s(68)

        // 配置空状态
        tv.em.emptyView = LNEmptyView.empty(
            firstReloadHidden: true,
            canTouch: false,
            isUserInteractionEnabled: false,
            offsetY: -s(80),
            space: s(15),
            backColor: .clear
        ) { config in
            config.image = UIImage(named: "ic_empty")
            config.imageSize = CGSize(width: s(120), height: s(120))
            config.titleTopSpace = s(20)
            config.title = "No data available for the time being."
            config.titleFont = LNFont.regular(16)
            config.titleColor = UIColor.systemGray
        } closure: { tag in
            print("Empty view tapped with tag: \(tag)")
        }

        return tv
    }()

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Blacklist"
        setupUI()
        setupRefresh()
        loadData(reset: true)
    }

    private func setupUI() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.bottom.equalToSuperview()
        }
    }

    private func setupRefresh() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(onRefresh))
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(onLoadMore))
        // 初始时隐藏footer，直到有数据时才显示
        tableView.mj_footer?.isHidden = true
    }

    @objc private func onRefresh() {
        loadData(reset: true)
    }

    @objc private func onLoadMore() {
        loadData(reset: false)
    }

    private func loadData(reset: Bool) {
        if reset {
            current = 1
            hasMore = true
            tableView.mj_footer?.resetNoMoreData()
        }

        // 调用黑名单列表接口
        let params = [
            "current": current,
            "size": size
        ]

        NetWorkRequest(LNApiProfile.blackList(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            // 解析响应数据
            if let dataDict = result["data"] as? [String: Any],
               let jsonData = try? JSONSerialization.data(withJSONObject: dataDict),
               let pageResponse = LNPageResponse<LNBlacklistUser>.deserialize(from: String(data: jsonData, encoding: .utf8)) {

                let newItems = pageResponse.records

                if reset {
                    self.data = newItems
                } else {
                    self.data.append(contentsOf: newItems)
                }

                // 更新分页信息
                self.current = pageResponse.current + 1
                self.hasMore = pageResponse.current < pageResponse.pages

                DispatchQueue.main.async {
                    self.tableView.reloadData()
                    self.tableView.mj_header?.endRefreshing()

                    // 只有当有数据时才处理footer状态
                    if self.data.isEmpty {
                        // 数据为空时，隐藏footer
                        self.tableView.mj_footer?.endRefreshing()
                        self.tableView.mj_footer?.isHidden = true
                    } else {
                        // 有数据时，显示footer并根据是否还有更多数据设置状态
                        self.tableView.mj_footer?.isHidden = false
                        if self.hasMore {
                            self.tableView.mj_footer?.endRefreshing()
                        } else {
                            self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                        }
                    }
                }
            } else {
                // 解析失败，结束刷新
                DispatchQueue.main.async {
                    self.tableView.mj_header?.endRefreshing()
                    self.tableView.mj_footer?.endRefreshing()
                    // 如果数据为空，隐藏footer
                    if self.data.isEmpty {
                        self.tableView.mj_footer?.isHidden = true
                    }
                }
            }
        }, failure: { [weak self] error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.tableView.mj_header?.endRefreshing()
                self.tableView.mj_footer?.endRefreshing()

                // 如果数据为空，隐藏footer
                if self.data.isEmpty {
                    self.tableView.mj_footer?.isHidden = true
                }

                // 显示错误提示
                print("黑名单列表加载失败: \(error.localizedDescription)")
            }
        })
    }

    // MARK: - Table
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { data.count }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: LNBlacklistCell.reuseId, for: indexPath) as! LNBlacklistCell
        let user = data[indexPath.row]

        // 创建兼容的BlockUser结构用于cell配置
        let blockUser = LNBlockUser(
            name: user.displayName,
            ageTag: user.ageTag,
            idTag: user.idTag,
            avatarURL: user.blackHeadFileName.isEmpty ? nil : user.blackHeadFileName
        )

        cell.configure(with: blockUser)
        cell.onRemove = { [weak self] in
            guard let self = self else { return }
            self.removeBlackUser(at: indexPath.row)
        }
        return cell
    }

    // MARK: - Remove User
    private func removeBlackUser(at index: Int) {
        let user = data[index]

        // 调用取消拉黑接口
        let params = ["blackUserId": user.blackUserId]

        NetWorkRequest(LNApiProfile.removeBlack(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                // 从数据源移除
                self.data.remove(at: index)

                // 更新UI
                self.tableView.performBatchUpdates({
                    self.tableView.deleteRows(at: [IndexPath(row: index, section: 0)], with: .automatic)
                }, completion: { _ in
                    if self.data.isEmpty {
                        // 数据为空时，隐藏footer
                        self.tableView.mj_footer?.isHidden = true
                    }
                })
            }
        }, failure: { error in
            DispatchQueue.main.async {
                print("取消拉黑失败: \(error.localizedDescription)")
                // 可以在这里显示错误提示
            }
        })
    }

}

