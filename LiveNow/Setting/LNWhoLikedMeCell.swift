//
//  LNWhoLikedMeCell.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/14.
//

import UIKit
import SnapKit

final class LNWhoLikedMeCell: UITableViewCell {
    static let reuseId = "LNWhoLikedMeCell"

    var onVideoCall: (() -> Void)?

    // MARK: - UI Components
    private lazy var avatarView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        iv.layer.cornerRadius = s(24)
        iv.backgroundColor = UIColor.systemGray5
        return iv
    }()

    private lazy var nameLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.forTextStyle(.headline)
        l.textColor = UIColor.label
        l.adjustsFontForContentSizeCategory = true
        return l
    }()

    private lazy var ageGenderBadge: UILabel = Self.makeBadge(bg: UIColor.hex(hexString: "#FF77C9"), textColor: .white)
    private lazy var countryBadge: UILabel = Self.makeBadge(bg: UIColor.hex(hexString: "#44E3D0"), textColor: .white)

    private lazy var videoCallButton: UIButton = {
        let b = UIButton(type: .system)
        b.backgroundColor = UIColor.hex(hexString: "#44E3D0")
        b.layer.cornerRadius = s(20)
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(videoCallTapped), for: .touchUpInside)
        
        // 创建摄像头图标
        let config = UIImage.SymbolConfiguration(pointSize: s(16), weight: .medium)
        let cameraImage = UIImage(systemName: "video.fill", withConfiguration: config)
        b.setImage(cameraImage, for: .normal)
        b.tintColor = .white
        
        return b
    }()

    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .white
        setupUI()
    }

    required init?(coder: NSCoder) { 
        fatalError("init(coder:) has not been implemented") 
    }

    private func setupUI() {
        contentView.addSubview(avatarView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(ageGenderBadge)
        contentView.addSubview(countryBadge)
        contentView.addSubview(videoCallButton)

        avatarView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(48))
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(12))
            make.left.equalTo(avatarView.snp.right).offset(s(12))
            make.right.lessThanOrEqualTo(videoCallButton.snp.left).offset(-s(8))
        }
        
        ageGenderBadge.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(s(8))
            make.height.equalTo(s(20))
        }
        
        countryBadge.snp.makeConstraints { make in
            make.left.equalTo(ageGenderBadge.snp.right).offset(s(8))
            make.centerY.equalTo(ageGenderBadge)
            make.height.equalTo(s(20))
            make.right.lessThanOrEqualTo(videoCallButton.snp.left).offset(-s(12))
        }
        
        videoCallButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-s(16))
            make.width.height.equalTo(s(40))
        }
    }

    // MARK: - Configuration
    func configure(with user: LNWhoLikedMeViewController.LikedUser) {
        avatarView.image = user.avatar
        nameLabel.text = user.name
        ageGenderBadge.text = "  \(user.ageGender)  "
        countryBadge.text = "  \(user.country)  "
    }

    // MARK: - Actions
    @objc private func videoCallTapped() { 
        onVideoCall?() 
    }

    // MARK: - Helper Methods
    private static func makeBadge(bg: UIColor, textColor: UIColor) -> UILabel {
        let l = UILabel()
        l.font = LNFont.forTextStyle(.caption1)
        l.textColor = textColor
        l.backgroundColor = bg
        l.layer.cornerRadius = s(10)
        l.layer.masksToBounds = true
        l.textAlignment = .center
        l.setContentHuggingPriority(.required, for: .horizontal)
        l.setContentCompressionResistancePriority(.required, for: .horizontal)
        return l
    }
}
