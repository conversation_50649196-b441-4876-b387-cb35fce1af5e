//
//  LNBlacklistModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/21.
//

import Foundation
import UIKit
import HandyJSON

// MARK: - 黑名单用户数据模型
/// 黑名单用户数据模型
class LNBlacklistUser: BaseModel {
    var blackAge: Int = 0
    var blackHeadFileName: String = ""
    var blackLevel: String = ""
    var blackNickName: String = ""
    var blackRole: String = ""
    var blackUserId: Int = 0
    var country: String = ""
    var createTime: String = ""
    var id: Int = 0
    var updateTime: String = ""
    var userId: Int = 0
    
    /// 计算年龄标签
    var ageTag: String {
        return blackAge > 0 ? "♀︎\(blackAge)" : "♀︎22"
    }
    
    /// 计算ID标签
    var idTag: String {
        return "ID:\(blackUserId)"
    }
    
    /// 显示名称
    var displayName: String {
        return blackNickName.isEmpty ? "User" : blackNickName
    }
}

// MARK: - 分页响应数据模型
/// 通用分页响应数据模型
class LNPageResponse<T: BaseModel>: BaseModel {
    var current: Int = 0
    var hitCount: Bool = true
    var pages: Int = 0
    var records: [T] = []
    var searchCount: Bool = true
    var size: Int = 0
    var total: Int = 0
}

// MARK: - API响应模型
/// 黑名单API响应模型
class LNBlacklistResponse: BaseModel {
    var code: Int = 0
    var data: LNPageResponse<LNBlacklistUser>?
    var msg: String = ""
    var success: Bool = false
}

// MARK: - 兼容性模型
/// 为了兼容现有的Cell，保留BlockUser结构
struct LNBlockUser {
    let name: String
    let ageTag: String
    let idTag: String
    let avatarURL: String? // 头像URL
}
