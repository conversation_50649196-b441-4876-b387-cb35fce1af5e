//
//  LNMyVipLevelController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/15.
//

import UIKit
import SnapKit
import FSPagerView

/// VIP等级页面
/// 显示用户VIP等级信息、轮播图、权益列表等
class LNMyVipLevelController: LNBaseController {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .white }

    // MARK: - Data Models
    struct VipLevelInfo {
        let level: Int
        let title: String
        let description: String
        let icon: String
        let progress: Int // 到下一级的进度
        let nextLevelRequirement: Int
    }

    struct VipPrivilege {
        let title: String
        let description: String
        let icon: String
    }

    // MARK: - Properties
    private var currentVipInfo = VipLevelInfo(
        level: 0,
        title: "V0",
        description: "100 to the next level",
        icon: "ic_vip_v0",
        progress: 100,
        nextLevelRequirement: 100
    )

    private let vipPrivileges = [
        VipPrivilege(title: "VIP 权益一", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益二", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益三", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益四", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益五", description: "Free times to send messages", icon: "ic_vip_star"),
        VipPrivilege(title: "VIP 权益六", description: "Free times to send messages", icon: "ic_vip_star")
    ]

    // MARK: - UI Elements
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_vip_bg")
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        return imageView
    }()

    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = .clear
        return scrollView
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 顶部轮播
    private lazy var pagerView: FSPagerView = {
        let pager = FSPagerView()
        pager.dataSource = self
        pager.delegate = self
        pager.register(FSPagerViewCell.self, forCellWithReuseIdentifier: "cell")
        pager.itemSize = CGSize(width: s(300), height: s(160))
        pager.interitemSpacing = s(20)
        pager.isInfinite = true
        pager.automaticSlidingInterval = 3.0
        pager.backgroundColor = .clear
        return pager
    }()

    // 中间渐变按钮
    private lazy var privilegeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("LV 0-5 Privilege", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.layer.cornerRadius = s(25)
        button.clipsToBounds = true
        return button
    }()

    private lazy var privilegeButtonGradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor.hex(hexString: "#F4F46D").cgColor,
            UIColor.hex(hexString: "#26FFDB").cgColor
        ]
        layer.startPoint = CGPoint(x: 0, y: 0.5)
        layer.endPoint = CGPoint(x: 1, y: 0.5)
        return layer
    }()

    // 权益列表容器
    private lazy var privilegesContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.15)
        view.layer.cornerRadius = s(20)
        view.clipsToBounds = true
        return view
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupGradientButton()
        createPrivilegeViews()
        setupNavigationBar()
    }

    private func setupNavigationBar() {
        // 设置返回按钮为白色
        if let nav = navigationController, nav.viewControllers.count > 1 {
            let backButton = UIBarButtonItem(
                image: UIImage(systemName: "chevron.left"),
                style: .plain,
                target: self,
                action: #selector(onBackButtonItem)
            )
            backButton.tintColor = .white
            navigationItem.leftBarButtonItem = backButton
        }
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        privilegeButtonGradientLayer.frame = privilegeButton.bounds
    }

    // MARK: - Setup Methods
    private func setupUI() {
        title = "Level"
        view.backgroundColor = .clear

        view.addSubview(backgroundImageView)
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(pagerView)
        contentView.addSubview(privilegeButton)
        contentView.addSubview(privilegesContainer)
    }

    private func setupConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(-knavH)
            make.bottom.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        pagerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(180))
        }

        privilegeButton.snp.makeConstraints { make in
            make.top.equalTo(pagerView.snp.bottom).offset(s(30))
            make.left.equalToSuperview().offset(s(20))
            make.right.equalToSuperview().offset(s(-20))
            make.height.equalTo(s(50))
        }

        privilegesContainer.snp.makeConstraints { make in
            make.top.equalTo(privilegeButton.snp.bottom).offset(s(30))
            make.left.equalToSuperview().offset(s(20))
            make.right.equalToSuperview().offset(s(-20))
            make.bottom.equalToSuperview().offset(s(-20))
        }
    }

    private func setupGradientButton() {
        privilegeButton.layer.insertSublayer(privilegeButtonGradientLayer, at: 0)
    }

    private func createPrivilegeViews() {
        var previousView: UIView?

        for (index, privilege) in vipPrivileges.enumerated() {
            let privilegeView = createPrivilegeItemView(privilege: privilege)
            privilegesContainer.addSubview(privilegeView)

            privilegeView.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(s(20))
                make.right.equalToSuperview().offset(s(-20))
                make.height.equalTo(s(60))

                if let previous = previousView {
                    make.top.equalTo(previous.snp.bottom).offset(s(15))
                } else {
                    make.top.equalToSuperview().offset(s(20))
                }

                if index == vipPrivileges.count - 1 {
                    make.bottom.equalToSuperview().offset(s(-20))
                }
            }

            previousView = privilegeView
        }
    }

    private func createPrivilegeItemView(privilege: VipPrivilege) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .clear

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(named: privilege.icon)
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = UIColor.hex(hexString: "#26FFDB")

        let titleLabel = UILabel()
        titleLabel.text = privilege.title
        titleLabel.font = LNFont.medium(16)
        titleLabel.textColor = .white

        let descriptionLabel = UILabel()
        descriptionLabel.text = privilege.description
        descriptionLabel.font = LNFont.regular(14)
        descriptionLabel.textColor = UIColor.white.withAlphaComponent(0.8)

        containerView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)

        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(24))
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(s(15))
            make.top.equalToSuperview().offset(s(8))
            make.right.equalToSuperview()
        }

        descriptionLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(s(4))
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(s(-8))
        }

        return containerView
    }
}

// MARK: - FSPagerView DataSource & Delegate
extension LNMyVipLevelController: FSPagerViewDataSource, FSPagerViewDelegate {

    func numberOfItems(in pagerView: FSPagerView) -> Int {
        return 3 // 显示3个轮播项
    }

    func pagerView(_ pagerView: FSPagerView, cellForItemAt index: Int) -> FSPagerViewCell {
        let cell = pagerView.dequeueReusableCell(withReuseIdentifier: "cell", at: index)

        // 创建VIP等级卡片
        cell.contentView.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        cell.contentView.layer.cornerRadius = s(20)
        cell.contentView.clipsToBounds = true

        // 清除之前的子视图
        cell.contentView.subviews.forEach { $0.removeFromSuperview() }

        // 创建VIP等级卡片内容
        let vipIconImageView = UIImageView()
        vipIconImageView.image = UIImage(named: "ic_vip_v\(index)")
        vipIconImageView.contentMode = .scaleAspectFit

        let levelLabel = UILabel()
        levelLabel.text = "V\(index)"
        levelLabel.font = LNFont.medium(24)
        levelLabel.textColor = UIColor.white.withAlphaComponent(0.8)

        let progressLabel = UILabel()
        progressLabel.text = "\(100 * (index + 1)) to the next level"
        progressLabel.font = LNFont.regular(14)
        progressLabel.textColor = UIColor.white.withAlphaComponent(0.7)

        let crownImageView = UIImageView()
        crownImageView.image = UIImage(named: "ic_vip_status")
        crownImageView.contentMode = .scaleAspectFit

        cell.contentView.addSubview(vipIconImageView)
        cell.contentView.addSubview(levelLabel)
        cell.contentView.addSubview(progressLabel)
        cell.contentView.addSubview(crownImageView)

        vipIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(20))
            make.top.equalToSuperview().offset(s(20))
            make.width.height.equalTo(s(40))
        }

        levelLabel.snp.makeConstraints { make in
            make.left.equalTo(vipIconImageView.snp.right).offset(s(10))
            make.centerY.equalTo(vipIconImageView)
        }

        progressLabel.snp.makeConstraints { make in
            make.left.equalTo(vipIconImageView)
            make.top.equalTo(vipIconImageView.snp.bottom).offset(s(15))
            make.right.equalToSuperview().offset(s(-20))
        }

        crownImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(s(-20))
            make.top.equalToSuperview().offset(s(20))
            make.width.height.equalTo(s(60))
        }

        return cell
    }

    func pagerView(_ pagerView: FSPagerView, didSelectItemAt index: Int) {
        // 处理轮播项点击事件
        print("Selected VIP level: V\(index)")
    }
}