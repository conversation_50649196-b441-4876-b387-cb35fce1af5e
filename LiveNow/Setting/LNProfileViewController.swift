//
//  LNProfileViewController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import SnapKit

/// 个人页视图控制器 - 用户个人信息和设置
class LNProfileViewController: LNBaseController {

    // 使用透明导航栏背景，避免转场时的空白问题
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }


    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        edgesForExtendedLayout = .all

        setupUI()
        setupConstraints()
        setupActions()
        createStatsCards()
        createMenuItems()

        // 配置用户数据
        configureUserData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 每次页面出现时刷新用户数据
        configureUserData()
    }
    
    // MARK: - Private Methods

    /// 配置用户数据显示
    private func configureUserData() {
        guard let user = LNUserManager.shared.userModel else {
            // 如果没有用户数据，显示默认值
            configureDefaultUserData()
            return
        }

        // 配置用户头像
        loadUserAvatar(user.headFileName)

        // 配置用户昵称
        nameLabel.text = user.nickName.isEmpty ? "NICKNAME" : user.nickName

        // 配置性别年龄徽标
        let genderSymbol = user.gender == 1 ? "♂︎" : (user.gender == 2 ? "♀︎" : "")
        let ageText = user.age != -1 ? user.age : 0
        genderAgeBadge.text = "\(genderSymbol)\(ageText)"

        // 根据性别设置徽标颜色
        if user.gender == 1 {
            genderAgeBadge.backgroundColor = UIColor(hexString: "#77DBFF") // 男性蓝色
        } else if user.gender == 2 {
            genderAgeBadge.backgroundColor = UIColor(hexString: "#FF77B7") // 女性粉色
        } else {
            genderAgeBadge.backgroundColor = UIColor(hexString: "#77DBFF") // 默认蓝色
        }

        // 配置位置信息
        locationLabel.text = user.country.isEmpty ? "Unknown" : user.country

        // 配置用户ID
        idLabel.text = "ID:\(user.id)"

        // 配置钻石数量
        updateDiamondCount(user.diamond)

        // 配置VIP状态
        updateVIPStatus(user)

        // 配置等级信息
        updateLevelInfo(user)
    }

    /// 配置默认用户数据（未登录或数据为空时）
    private func configureDefaultUserData() {
        nameLabel.text = "NICKNAME"
        genderAgeBadge.text = "♂︎30"
        genderAgeBadge.backgroundColor = UIColor(hexString: "#77DBFF")
        locationLabel.text = "Indonesia"
        idLabel.text = "ID:11223344"
        updateDiamondCount(999)
        updateVIPStatus(nil)
        updateLevelInfo(nil)
    }

    /// 更新钻石数量显示
    private func updateDiamondCount(_ count: Int) {
        diamondValueLabel?.text = "\(count)"
    }

    /// 更新VIP状态显示
    private func updateVIPStatus(_ user: LNUserModel?) {
        guard let user = user else {
            updateVIPCardStatus("Unactivated")
            return
        }

        // 检查VIP状态
        if user.vipExpireFlag == 1 && !user.vipExpireDay.isEmpty {
            updateVIPCardStatus("Active")
        } else {
            updateVIPCardStatus("Unactivated")
        }
    }

    /// 更新VIP卡片状态
    private func updateVIPCardStatus(_ status: String) {
        vipValueLabel?.text = status
    }

    /// 更新等级信息
    private func updateLevelInfo(_ user: LNUserModel?) {
        guard let user = user else {
            nextLevelLabel.text = "100 to the next level"
            levelProgressView.progress = 0.3
            levelCardBackgroundImageView.image = UIImage(named: "ic_vip_v0")
            return
        }

        // 更新等级背景图
        let levelKey = user.levelKey
        let levelImageName = "ic_vip_v\(levelKey)"
        levelCardBackgroundImageView.image = UIImage(named: levelImageName) ?? UIImage(named: "ic_vip_v0")

        // 更新下一等级所需经验
        let nextDiff = user.nextGradeDiff
        if user.nextGradeDiff != -1 {
            nextLevelLabel.text = "\(nextDiff) to the next level"
        } else {
            nextLevelLabel.text = "Max level reached"
        }

        // 计算等级进度
        let current = Float(user.currentGradeStd)
        let next = Float(user.nextGradeStd)
        if user.currentGradeStd != -1 && user.nextGradeStd != -1, next > 0 {
            let progress = current / next
            levelProgressView.progress = min(max(progress, 0.0), 1.0)
        } else {
            levelProgressView.progress = 1.0 // 满级
        }
    }

    private func setupUI() {
        // 添加子视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(headerView)
       

        headerView.addSubview(avatarImageView)
        headerView.addSubview(nameLabel)
        headerView.addSubview(genderAgeBadge)
        headerView.addSubview(locationIcon)
        headerView.addSubview(locationLabel)
        headerView.addSubview(idCard)
        idCard.addSubview(idLabel)
        idCard.addSubview(copyIcon)
        headerView.addSubview(headerChevron)

        contentView.addSubview(cardsContainerView)
        cardsContainerView.addSubview(levelCardView)
        levelCardView.addSubview(levelCardBackgroundImageView)
        levelCardView.addSubview(nextLevelLabel)
        levelCardView.addSubview(levelProgressView)

        cardsContainerView.addSubview(statsStackView)

        contentView.addSubview(menuContainerView)
        menuContainerView.addSubview(menuStackView)
    }

    private func setupConstraints() {
        // ScrollView约束
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(kstatusBarH)
            make.left.right.bottom.equalToSuperview()
        }

        // ContentView约束
        contentView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.width.equalToSuperview()
        }

        // Header View约束
        headerView.snp.makeConstraints { make in
            make.top.equalTo(25)
            make.left.right.equalToSuperview()
            make.height.equalTo(s(100))
        }

        // Avatar Image View约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(80))
        }

        // Name Label约束
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(s(16))
            make.top.equalTo(avatarImageView.snp.top).offset(s(8))
            make.right.lessThanOrEqualTo(genderAgeBadge.snp.left).offset(-s(8))
        }

        // 性别年龄徽标约束
        genderAgeBadge.snp.makeConstraints { make in
            make.centerY.equalTo(nameLabel)
            make.left.equalTo(nameLabel.snp.right).offset(s(8))
            make.width.greaterThanOrEqualTo(s(40))
            make.height.equalTo(s(20))
        }

        locationIcon.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(s(6))
            make.width.height.equalTo(s(16))
        }

        // 位置标签约束
        locationLabel.snp.makeConstraints { make in
            make.left.equalTo(locationIcon.snp.right).offset(s(4))
            make.top.equalTo(nameLabel.snp.bottom).offset(s(6))
            make.right.lessThanOrEqualTo(headerChevron.snp.left).offset(-s(8))
        }

        idCard.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(locationLabel.snp.bottom).offset(s(8))
            make.right.lessThanOrEqualTo(headerChevron.snp.left).offset(-s(8))
            make.height.equalTo(s(20))
        }

        copyIcon.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(10))
        }
        // ID标签约束
        idLabel.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.right.equalTo(copyIcon.snp.left).offset(-s(4))
        }
   

        // 右箭头约束
        headerChevron.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(20))
            make.centerY.equalTo(avatarImageView)
            make.width.height.equalTo(s(16))
        }

        // 卡片容器约束
        cardsContainerView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
            make.height.equalTo(s(242))
        }

        // 等级卡片约束
        levelCardView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(s(130))
        }
        
        levelCardBackgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }


        levelProgressView.snp.makeConstraints { make in
            make.left.equalTo(s(16))
            make.right.equalTo(-s(16))
            make.height.equalTo(s(6))
            make.bottom.equalTo(-s(6))
        }

        nextLevelLabel.snp.makeConstraints { make in
            make.left.equalTo(s(16))
            make.bottom.equalTo(levelProgressView.snp.top).offset(-s(8))
        }

        // 统计卡片堆栈约束
        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(levelCardView.snp.bottom).offset(s(12))
            make.left.right.bottom.equalToSuperview()
        }

        // 菜单容器约束
        menuContainerView.snp.makeConstraints { make in
            make.top.equalTo(cardsContainerView.snp.bottom).offset(s(12))
            make.left.right.equalToSuperview().inset(s(16))
            make.bottom.equalToSuperview().offset(-s(100)) // 增加底部间距，避免被TabBar遮挡
        }

        // Menu Stack View约束
        menuStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func createStatsCards() {
        let user = LNUserManager.shared.userModel

        // 钻石卡片 - 使用真实钻石数量
        let diamondCount = user?.diamond ?? 0
        let diamondCard = createStatsCard(
            title: "Diamond",
            value: "\(diamondCount)",
            color: UIColor.systemGreen,
            icon: "diamond",
            backgroundImageName: "ic_me_diamond_count"
        )

        // VIP卡片 - 使用真实VIP状态
        let vipStatus: String
        if let user = user, user.vipExpireFlag == 1 && !user.vipExpireDay.isEmpty {
            vipStatus = "Active"
        } else {
            vipStatus = "Unactivated"
        }

        let vipCard = createStatsCard(
            title: "VIP",
            value: vipStatus,
            color: UIColor.systemOrange,
            icon: "crown.fill",
            backgroundImageName: "ic_vip_status"
        )

        // 为VIP卡片添加点击手势，跳转到会员中心
        let vipCardTapGesture = UITapGestureRecognizer(target: self, action: #selector(vipCardTapped))
        vipCard.addGestureRecognizer(vipCardTapGesture)
        vipCard.isUserInteractionEnabled = true

        statsStackView.addArrangedSubview(diamondCard)
        statsStackView.addArrangedSubview(vipCard)
    }

    private func createStatsCard(title: String, value: String, color: UIColor, icon: String, backgroundImageName: String? = nil) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = color.withAlphaComponent(0.15)
        cardView.layer.cornerRadius = 12
        cardView.layer.masksToBounds = true

        if let bgName = backgroundImageName, let bgImage = UIImage(named: bgName) {
            let bgImageView = UIImageView(image: bgImage)
            bgImageView.contentMode = .scaleAspectFill
            bgImageView.clipsToBounds = true
            cardView.addSubview(bgImageView)
            bgImageView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = LNFont.medium(14)
        titleLabel.textColor = color

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = LNFont.bold(14)
        valueLabel.textColor = color

        // 保存标签引用到全局变量
        if title == "Diamond" {
            diamondValueLabel = valueLabel
        } else if title == "VIP" {
            vipValueLabel = valueLabel
        }

        cardView.addSubview(titleLabel)
        cardView.addSubview(valueLabel)

        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(12))
            make.top.equalToSuperview().offset(s(12))
        }

        valueLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(s(14))
        }

        return cardView
    }

    private func createMenuItems() {
        let menuItems = [
            ("ic_me_likes", "Who liked me", ""),
            ("ic_vip_star", "Membership Center", ""),
            ("ic_me_tasks", "tasks", ""),
            ("ic_me_diamond", "Diamond History", ""),
            ("ic_vip_star", "Subscription History", ""),
            ("ic_me_service", "customer Service", ""),
            ("ic_me_setting", "Setting", ""),
            ("ic_me_setting", "Complete Data Demo", ""),
            ("ic_me_setting", "Person Info Demo", "")
        ]

        for (index, item) in menuItems.enumerated() {
            let menuItem = createMenuItem(icon: item.0, title: item.1, subtitle: item.2, tag: index)
            menuStackView.addArrangedSubview(menuItem)
        }
    }

    private func createMenuItem(icon: String, title: String, subtitle: String, tag: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = UIColor.clear
        containerView.tag = tag

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(named: icon)
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = LNFont.medium(16)
        titleLabel.textColor = UIColor.label

        let chevronImageView = UIImageView()
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.systemGray3
        chevronImageView.contentMode = .scaleAspectFit

        containerView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(chevronImageView)

        // 使用SnapKit设置约束
        containerView.snp.makeConstraints { make in
            make.height.equalTo(s(50))
        }

        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(24))
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(s(16))
            make.centerY.equalToSuperview()
            make.right.equalTo(chevronImageView.snp.left).offset(-s(16))
        }

        chevronImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(12))
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(menuItemTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)

        return containerView
    }

    private func setupActions() {
        // 为整个 header 添加点击手势，跳转到个人信息页面
        let headerTapGesture = UITapGestureRecognizer(target: self, action: #selector(headerTapped))
        headerView.addGestureRecognizer(headerTapGesture)
        headerView.isUserInteractionEnabled = true

        // 为等级卡片添加点击手势，跳转到VIP等级页面
        let levelCardTapGesture = UITapGestureRecognizer(target: self, action: #selector(levelCardTapped))
        levelCardView.addGestureRecognizer(levelCardTapGesture)
        levelCardView.isUserInteractionEnabled = true

        // 为ID卡片添加点击手势，复制用户ID
        let idCardTapGesture = UITapGestureRecognizer(target: self, action: #selector(idCardTapped))
        idCard.addGestureRecognizer(idCardTapGesture)
        idCard.isUserInteractionEnabled = true
    }

    @objc private func headerTapped() {
        let personalDataVC = LNPersonalDataViewController()
        personalDataVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(personalDataVC, animated: true)
    }

    @objc private func levelCardTapped() {
        let vipLevelVC = LNMyVipLevelController()
        vipLevelVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(vipLevelVC, animated: true)
    }

    @objc private func vipCardTapped() {
        let membershipVC = LNMembershipCenterViewController()
        membershipVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(membershipVC, animated: true)
    }

    @objc private func idCardTapped() {
        guard let user = LNUserManager.shared.userModel else { return }

        let userIdString = "\(user.id)"
        UIPasteboard.general.string = userIdString

        // 显示复制成功提示
        let alert = UIAlertController(title: "Copied", message: "User ID copied to clipboard", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    /// 加载用户头像
    private func loadUserAvatar(_ headFileName: String) {
        if headFileName.isEmpty {
            // 使用默认头像
            avatarImageView.image = UIImage(systemName: "person.circle.fill")
            avatarImageView.tintColor = UIColor.white
            return
        }

        // 如果有头像文件名，尝试加载
        if let url = URL(string: headFileName) {
            // 这里可以使用SDWebImage或其他图片加载库
            // 目前先使用简单的异步加载
            URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
                guard let data = data, let image = UIImage(data: data) else {
                    DispatchQueue.main.async {
                        self?.avatarImageView.image = UIImage(systemName: "person.circle.fill")
                        self?.avatarImageView.tintColor = UIColor.white
                    }
                    return
                }

                DispatchQueue.main.async {
                    self?.avatarImageView.image = image
                    self?.avatarImageView.tintColor = nil
                }
            }.resume()
        } else {
            // 如果不是有效URL，尝试作为本地图片名称
            if let localImage = UIImage(named: headFileName) {
                avatarImageView.image = localImage
                avatarImageView.tintColor = nil
            } else {
                avatarImageView.image = UIImage(systemName: "person.circle.fill")
                avatarImageView.tintColor = UIColor.white
            }
        }
    }

    @objc private func menuItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        if view.tag == 0 {
            let vc = LNWhoLikedMeViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 1 {
            let vc = LNMembershipCenterViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 2 {
            let vc = LNRewardTasksViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 3 {
            let vc = LNDiamondHistoryViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 4 {
            let vc = LNSubscriptionHistoryViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 5 {
            let vc = LNCustomerServiceViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 6 {
            let vc = LNSettingViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 7 {
            let vc = LNCompleteDataViewController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
        if view.tag == 8 {
            let vc = LNPersonInfoController()
            vc.hidesBottomBarWhenPushed = true
            navigationController?.pushViewController(vc, animated: true)
            return
        }
    }

    // MARK: - UI Elements

    // 全局变量保存标签引用，避免嵌套循环查找
    private var diamondValueLabel: UILabel?
    private var vipValueLabel: UILabel?

    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.bounces = false
        // 禁用自动内容偏移调整，避免系统自动添加导航栏高度的偏移
        scrollView.contentInsetAdjustmentBehavior = .never
        return scrollView
    }()
    private let contentView = UIView()

    private let headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 确保背景透明，不遮挡渐变层
        return view
    }()



    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "person.circle.fill")
        imageView.tintColor = UIColor.white
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = s(40)
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        return imageView
    }()

    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = "NICKNAME"
        label.font = LNFont.bold(18)
        label.textColor = UIColor(hexString: "#1F1F1F")
        label.textAlignment = .left
        return label
    }()

    private let menuStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fill
        return stackView
    }()


    // 名称右侧性别年龄徽标
    private lazy var genderAgeBadge: UILabel = {
        let label = UILabel()
        label.text = "♂︎30"
        label.font = LNFont.medium(12)
        label.textColor = .white
        label.textAlignment = .center
        label.backgroundColor = UIColor(hexString: "#77DBFF")
        label.layer.cornerRadius = 10
        label.clipsToBounds = true
        label.setContentHuggingPriority(.required, for: .horizontal)
        label.setContentCompressionResistancePriority(.required, for: .horizontal)
        return label
    }()

    // 位置与ID
    private lazy var locationLabel: UILabel = {
        let label = UILabel()
        label.text = "Unknown"
        label.font = LNFont.regular(13)
        label.textColor = UIColor(hexString: "#C0C0C0")
        label.numberOfLines = 1
        return label
    }()

    private let locationIcon: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "ic_location")
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    private var idCard: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hexString: "#D7FFEF")
        v.layer.cornerRadius = 4
        v.layer.masksToBounds = true
        return v
    }()

    private lazy var idLabel: UILabel = {
        let label = UILabel()
        label.text = "ID:0"
        label.font = LNFont.regular(12)
        label.textColor = UIColor(hexString: "#D4ECF2")
        return label
    }()

    private lazy var copyIcon: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "ic_copy")
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    private let headerChevron: UIImageView = {
        let config = UIImage.SymbolConfiguration(weight: .bold)
        let iv = UIImageView(image: UIImage(systemName: "chevron.right", withConfiguration: config))
        iv.tintColor = UIColor(hexString: "#C0C0C0")
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    // 等级卡片
    private let levelCardView: UIView = {
        let v = UIView()
        v.layer.cornerRadius = 14
        v.layer.masksToBounds = true
        return v
    }()
    
    // 等级卡片背景图
    private let levelCardBackgroundImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "ic_vip_v0")
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        return iv
    }()
    
    private let nextLevelLabel: UILabel = {
        let l = UILabel()
        l.text = "100 to the next level"
        l.font = LNFont.regular(12)
        l.textColor = UIColor(hexString: "#6D75AD")
        return l
    }()

    private let levelProgressView: UIProgressView = {
        let p = UIProgressView(progressViewStyle: .default)
        p.progressTintColor = .white
        p.trackTintColor = UIColor.white.withAlphaComponent(0.3)
        p.layer.cornerRadius = 3
        p.clipsToBounds = true
        p.progress = 0.3
        return p
    }()

    // 钻石/VIP卡片
    private let statsStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.alignment = .fill
        stack.distribution = .fillEqually
        stack.spacing = s(15)
        stack.layer.cornerRadius = 16
        stack.layer.masksToBounds = true
        stack.backgroundColor = .white
        stack.isLayoutMarginsRelativeArrangement = true
        stack.layoutMargins = UIEdgeInsets(top: s(15), left: s(15), bottom: s(15), right: s(15))
        return stack
    }()

    // 菜单分组容器
    private let menuContainerView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.white
        v.layer.cornerRadius = 16
        v.layer.masksToBounds = true
        return v
    }()

    // 统计卡片外层容器
    private let cardsContainerView: UIView = {
        let v = UIView()
        return v
    }()

}
