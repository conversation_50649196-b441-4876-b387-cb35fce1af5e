//
//  LNWhoLikedMeViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/14.
//

import UIKit
import SnapKit
import MJRefresh

/// "Who Liked Me" 页面
/// 显示喜欢当前用户的用户列表，每个用户项包含头像、姓名、年龄性别标签、国家标签和视频通话按钮
class LNWhoLikedMeViewController: LNBaseController, UITableViewDataSource, UITableViewDelegate {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用顶部渐变背景
    override var useTopGradientBackground: Bool { return true }

    // MARK: - Model
    struct LikedUser {
        let name: String
        let ageGender: String
        let country: String
        let avatar: UIImage?
    }
    
    private var data: [LikedUser] = [
        .init(name: "User name", ageGender: "♀22", country: "Indonesia", avatar: UIImage(systemName: "person.crop.circle")),
        .init(name: "User name", ageGender: "♀22", country: "Indonesia", avatar: UIImage(systemName: "person.crop.circle")),
        .init(name: "User name", ageGender: "♀22", country: "Indonesia", avatar: UIImage(systemName: "person.crop.circle")),
        .init(name: "User name", ageGender: "♀22", country: "Indonesia", avatar: UIImage(systemName: "person.crop.circle")),
        .init(name: "User name", ageGender: "♀22", country: "Indonesia", avatar: UIImage(systemName: "person.crop.circle"))
    ]

    // 分页配置
    private var page: Int = 1
    private let pageSize: Int = 10
    private var hasMore: Bool = true

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.dataSource = self
        tv.delegate = self
        tv.separatorStyle = .none
        tv.backgroundColor = UIColor.systemGroupedBackground
        tv.register(LNWhoLikedMeCell.self, forCellReuseIdentifier: LNWhoLikedMeCell.reuseId)
        tv.rowHeight = s(68)
        return tv
    }()

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "who liked me"
        edgesForExtendedLayout = .all
        setupUI()
        setupRefresh()
    }

    private func setupUI() {
        view.addSubview(tableView)

        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }

    private func setupRefresh() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(onRefresh))
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(onLoadMore))
    }

    @objc private func onRefresh() {
        loadData(reset: true)
    }

    @objc private func onLoadMore() {
        loadData(reset: false)
    }

    private func loadData(reset: Bool) {
        if reset {
            page = 1
            hasMore = true
            tableView.mj_footer?.resetNoMoreData()
        }

        // 模拟分页请求
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }
            let newItems: [LikedUser] = (0..<self.pageSize).map { idx in
                .init(name: "User \(self.page)-\(idx)", ageGender: "♀22", country: "Indonesia", avatar: UIImage(systemName: "person.crop.circle"))
            }
            if reset { self.data = newItems } else { self.data.append(contentsOf: newItems) }
            self.page += 1
            self.hasMore = self.page <= 3

            self.tableView.reloadData()
            self.tableView.mj_header?.endRefreshing()
            if self.hasMore {
                self.tableView.mj_footer?.endRefreshing()
            } else {
                self.tableView.mj_footer?.endRefreshingWithNoMoreData()
            }
        }
    }

    // MARK: - Table
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { 
        return data.count 
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: LNWhoLikedMeCell.reuseId, for: indexPath) as! LNWhoLikedMeCell
        cell.configure(with: data[indexPath.row])
        cell.onVideoCall = { [weak self] in
            guard let self = self else { return }
            // 处理视频通话逻辑
            self.handleVideoCall(for: self.data[indexPath.row])
        }
        return cell
    }
    
    // MARK: - Actions
    private func handleVideoCall(for user: LikedUser) {
        // 这里可以集成视频通话功能
        let alert = UIAlertController(title: "Video Call", message: "Start video call with \(user.name)?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Call", style: .default) { _ in
            // 启动视频通话
            print("Starting video call with \(user.name)")
        })
        present(alert, animated: true)
    }
}
