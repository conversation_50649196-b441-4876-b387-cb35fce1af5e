//
//  LNPersonalDataViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit
import JKSwiftExtension
import Photos
import AVFoundation

// MARK: - 数据模型
public enum LNGender: String, CaseIterable {
    case male = "Male"
    case female = "Female"
    case other = "Other"

    var display: String { rawValue }
}

public struct LNPersonalData {
    public var avatar: UIImage?
    public var name: String
    public var birthDate: Date
    public var country: String
    public var gender: LNGender
}

/// 个人信息数据页（iOS 13，SnapKit 布局）
/// - 功能：
///   - 头像选择（UIImagePickerController）
///   - 姓名编辑（弹出输入框）
///   - 生日选择（inputView + UIDatePicker + 工具栏）
///   - 国家/性别选择（Action Sheet）
///   - 底部保存按钮回调 onSaved
class LNPersonalDataViewController: LNBaseController, UIImagePickerControllerDelegate, UINavigationControllerDelegate {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }
    // 使用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }
    
    private var selectedCountry: LNCountry? = nil

    // MARK: - Public
    public var onSaved: ((LNPersonalData) -> Void)?
    public var data: LNPersonalData = LNPersonalData(
        avatar: nil,
        name: "ABCCCD",
        birthDate: ISO8601DateFormatter().date(from: "1998-02-03T00:00:00Z") ?? Date(),
        country: "COUNTRY",
        gender: .male
    )

    // MARK: - UI
    private lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.alwaysBounceVertical = true
        v.keyboardDismissMode = .interactive
        v.isUserInteractionEnabled = true // 确保可以接收用户交互
        return v
    }()

    private lazy var contentView: UIView = {
        let v = UIView()
        v.isUserInteractionEnabled = true // 确保可以接收用户交互
        return v
    }()

    private lazy var avatarCard: UIView = Self.makeCard()
    private lazy var formCard: UIView = Self.makeCard()

    private lazy var avatarRow: UIView = {
        let v = UIView()
        v.isAccessibilityElement = true
        v.accessibilityLabel = "Avatar"
        let tap = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        v.addGestureRecognizer(tap)
        return v
    }()

    private lazy var avatarTitleLabel: UILabel = Self.makeTitleLabel(text: "Avatar")

    private lazy var avatarImageView: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFill
        iv.layer.cornerRadius = 20
        iv.clipsToBounds = true
        iv.backgroundColor = UIColor.systemGray5
        iv.image = UIImage(systemName: "person.crop.circle")
        iv.tintColor = .systemGray
        return iv
    }()

    private lazy var nameRow: UIView = Self.makeTappableRow(target: self, action: #selector(nameTapped))
    private lazy var nameTitleLabel: UILabel = Self.makeTitleLabel(text: "Name")
    private lazy var nameValueLabel: UILabel = Self.makeValueLabel()

    private lazy var dobRow: UIView = Self.makeTappableRow(target: self, action: #selector(dobTapped))
    private lazy var dobTitleLabel: UILabel = Self.makeTitleLabel(text: "Date of birth")
    private lazy var dobValueLabel: UILabel = Self.makeValueLabel()

    private lazy var countryRow: UIView = Self.makeTappableRow(target: self, action: #selector(countryTapped))
    private lazy var countryTitleLabel: UILabel = Self.makeTitleLabel(text: "Country")
    private lazy var countryValueLabel: UILabel = Self.makeValueLabel()

    private lazy var genderRow: UIView = Self.makeTappableRow(target: self, action: #selector(genderTapped))
    private lazy var genderTitleLabel: UILabel = Self.makeTitleLabel(text: "Gender")
    private lazy var genderValueLabel: UILabel = Self.makeValueLabel()

    private lazy var chevron1 = Self.makeChevron()
    private lazy var chevron2 = Self.makeChevron()
    private lazy var chevron3 = Self.makeChevron()
    private lazy var chevron4 = Self.makeChevron()

    private lazy var saveButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("save", for: .normal)
        b.titleLabel?.font = LNFont.forTextStyle(.headline)
        b.setTitleColor(.white, for: .normal)
        b.layer.cornerRadius = 24
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(saveTapped), for: .touchUpInside)
        return b
    }()

    // 保存按钮渐变层
    private let saveGradient = CAGradientLayer()



    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Personal Data"
        edgesForExtendedLayout = .all

        setupUI()
        setupConstraints()
        setupKeyboardHandling()

        // 获取用户信息
        fetchUserDetail()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        saveGradient.frame = saveButton.bounds
    }

    // MARK: - UI Setup
    private func setupUI() {

        view.addSubview(scrollView)
        view.addSubview(saveButton)
        scrollView.addSubview(contentView)

        contentView.addSubview(avatarCard)
        contentView.addSubview(formCard)

        // Avatar Card
        avatarCard.addSubview(avatarRow)
        avatarRow.addSubview(avatarTitleLabel)
        avatarRow.addSubview(avatarImageView)

        // Form Card rows
        formCard.addSubview(nameRow)
        formCard.addSubview(dobRow)
        formCard.addSubview(countryRow)
        formCard.addSubview(genderRow)

        // Name row subviews
        nameRow.addSubview(nameTitleLabel)
        nameRow.addSubview(nameValueLabel)
        nameRow.addSubview(chevron1)

        // DOB row subviews
        dobRow.addSubview(dobTitleLabel)
        dobRow.addSubview(dobValueLabel)
        dobRow.addSubview(chevron2)

        // Country row subviews
        countryRow.addSubview(countryTitleLabel)
        countryRow.addSubview(countryValueLabel)
        countryRow.addSubview(chevron3)

        // Gender row subviews
        genderRow.addSubview(genderTitleLabel)
        genderRow.addSubview(genderValueLabel)
        genderRow.addSubview(chevron4)

        // 设置保存按钮渐变色
        saveGradient.colors = LNGradient.primaryColors
        saveGradient.startPoint = LNGradient.primaryStartPoint
        saveGradient.endPoint = LNGradient.primaryEndPoint
        saveButton.layer.insertSublayer(saveGradient, at: 0)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.bottom.equalTo(saveButton.snp.top).offset(-16)
        }
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        avatarCard.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
        }
        avatarRow.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
            make.height.equalTo(56)
        }
        avatarTitleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(16)
        }
        avatarImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
            make.width.height.equalTo(40)
        }

        formCard.snp.makeConstraints { make in
            make.top.equalTo(avatarCard.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16) // 添加底部约束
        }

        // 每一行高度
        [nameRow, dobRow, countryRow, genderRow].forEach { row in
            row.snp.makeConstraints { make in
                make.left.right.equalToSuperview().inset(12)
                make.height.equalTo(56)
            }
        }
        nameRow.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
        }
        dobRow.snp.makeConstraints { make in
            make.top.equalTo(nameRow.snp.bottom)
        }
        countryRow.snp.makeConstraints { make in
            make.top.equalTo(dobRow.snp.bottom)
        }
        genderRow.snp.makeConstraints { make in
            make.top.equalTo(countryRow.snp.bottom)
            make.bottom.equalToSuperview().offset(-24)
        }

        // 标题与值布局
        func layoutRow(title: UILabel, value: UILabel, chevron: UIImageView, in row: UIView) {
            title.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                make.left.equalToSuperview().offset(16)
            }
            chevron.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                make.right.equalToSuperview().offset(-16)
                make.width.equalTo(8)
                make.height.equalTo(14)
            }
            value.snp.makeConstraints { make in
                make.centerY.equalToSuperview()
                make.right.equalTo(chevron.snp.left).offset(-8)
                make.left.greaterThanOrEqualTo(title.snp.right).offset(12)
            }
        }
        layoutRow(title: nameTitleLabel, value: nameValueLabel, chevron: chevron1, in: nameRow)
        layoutRow(title: dobTitleLabel, value: dobValueLabel, chevron: chevron2, in: dobRow)
        layoutRow(title: countryTitleLabel, value: countryValueLabel, chevron: chevron3, in: countryRow)
        layoutRow(title: genderTitleLabel, value: genderValueLabel, chevron: chevron4, in: genderRow)

        saveButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(32)
            make.height.equalTo(48)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-24)
        }
    }

    private func bindDataToUI() {
        avatarImageView.image = data.avatar ?? UIImage(systemName: "person.crop.circle")
        nameValueLabel.text = data.name
        dobValueLabel.text = Self.dateFormatter.string(from: data.birthDate)
        countryValueLabel.text = data.country
        genderValueLabel.text = data.gender.display
    }

    // MARK: - Actions
    @objc private func avatarTapped() {
        showAvatarPicker()
    }

    @objc private func nameTapped() {
        let alert = UIAlertController(title: "Name", message: nil, preferredStyle: .alert)
        alert.addTextField { tf in
            tf.text = self.data.name
            tf.clearButtonMode = .whileEditing
        }
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "确定", style: .default, handler: { _ in
            let newName = alert.textFields?.first?.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
            let finalName = newName.isEmpty ? self.data.name : newName

            // 更新本地数据模型
            self.data.name = finalName
            self.nameValueLabel.text = finalName

            // 直接更新 LNUserManager 中的用户模型
            if let user = LNUserManager.shared.userModel {
                user.nickName = finalName
            }
        }))
        present(alert, animated: true)
    }

    @objc private func dobTapped() {
        showDatePicker()
    }

    private func showAvatarPicker() {
        let modal = LNAvatarModal()
        modal.onSelectFromAlbum = { [weak self] in
            self?.checkPhotoLibraryPermission()
        }
        modal.onTakePhoto = { [weak self] in
            self?.checkCameraPermission()
        }
        modal.onCancel = {
            // 用户取消选择
        }
        // 使用 keyWindow 来覆盖整个屏幕包括导航栏
        if let keyWindow = JKPOP.keyWindow {
            modal.show(in: keyWindow)
        } else {
            modal.show(in: self.view)
        }
    }

    private func checkPhotoLibraryPermission() {
        let status = PHPhotoLibrary.authorizationStatus()
        switch status {
        case .authorized:
            presentImagePicker(sourceType: .photoLibrary)
        case .denied, .restricted:
            showPermissionAlert(for: "相册")
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { [weak self] newStatus in
                DispatchQueue.main.async {
                    self?.handlePhotoLibraryAuthorizationStatus(newStatus)
                }
            }
        case .limited:
            presentImagePicker(sourceType: .photoLibrary)
        @unknown default:
            // iOS 14+ 的 .limited 状态也允许访问
            presentImagePicker(sourceType: .photoLibrary)
        }
    }

    private func handlePhotoLibraryAuthorizationStatus(_ status: PHAuthorizationStatus) {
        if status == .authorized {
            presentImagePicker(sourceType: .photoLibrary)
        } else if #available(iOS 14, *), status == .limited {
            presentImagePicker(sourceType: .photoLibrary)
        } else {
            showPermissionAlert(for: "相册")
        }
    }

    private func checkCameraPermission() {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        switch status {
        case .authorized:
            presentImagePicker(sourceType: .camera)
        case .denied, .restricted:
            showPermissionAlert(for: "相机")
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                DispatchQueue.main.async {
                    if granted {
                        self?.presentImagePicker(sourceType: .camera)
                    } else {
                        self?.showPermissionAlert(for: "相机")
                    }
                }
            }
        @unknown default:
            showPermissionAlert(for: "相机")
        }
    }

    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        guard UIImagePickerController.isSourceTypeAvailable(sourceType) else {
            let message = sourceType == .camera ? "设备不支持相机功能" : "设备不支持相册功能"
            showAlert(title: "提示", message: message)
            return
        }

        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = self
        picker.allowsEditing = true
        present(picker, animated: true)
    }

    private func showPermissionAlert(for feature: String) {
        let alert = UIAlertController(
            title: "需要\(feature)权限",
            message: "请在设置中允许访问\(feature)以选择头像",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL)
            }
        })
        present(alert, animated: true)
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showCountryPicker() {
        let modal = LNCountryModal()
        modal.onCountrySelected = { [weak self] country in
            self?.selectedCountry = country
            self?.data.country = country.name
            self?.countryValueLabel.text = country.name

            // 直接更新 LNUserManager 中的用户模型
            if let user = LNUserManager.shared.userModel {
                user.country = country.name
            }
        }
        modal.onCancel = {
            // 用户取消选择
        }
        // 使用 keyWindow 来覆盖整个屏幕包括导航栏
        if let keyWindow = JKPOP.keyWindow {
            modal.show(in: keyWindow)
        } else {
            modal.show(in: self.view)
        }
    }

    private func showGenderPicker() {
        let modal = LNGenderModal()
        modal.onGenderSelected = { [weak self] gender in
            self?.data.gender = gender
            self?.genderValueLabel.text = gender.display

            // 直接更新 LNUserManager 中的用户模型
            if let user = LNUserManager.shared.userModel {
                switch gender {
                case .male:
                    user.gender = 1
                case .female:
                    user.gender = 2
                case .other:
                    user.gender = 0
                }
            }
        }
        modal.onCancel = {
            // 用户取消选择
        }
        // 使用 keyWindow 来覆盖整个屏幕包括导航栏
        if let keyWindow = JKPOP.keyWindow {
            modal.show(in: keyWindow)
        } else {
            modal.show(in: self.view)
        }
    }

    private func showDatePicker() {
        let modal = LNDateOfBirthModal()
        modal.onConfirm = { [weak self] date in
            self?.data.birthDate = date
            self?.dobValueLabel.text = Self.dateFormatter.string(from: date)

            // 直接更新 LNUserManager 中的用户模型
            if let user = LNUserManager.shared.userModel {
                user.birthday = Self.dateFormatter.string(from: date)
            }
        }
        modal.onCancel = {
            // 用户取消选择
        }
        // 使用 keyWindow 来覆盖整个屏幕包括导航栏
        if let keyWindow = JKPOP.keyWindow {
            modal.show(in: keyWindow, initialDate: data.birthDate)
        } else {
            modal.show(in: self.view, initialDate: data.birthDate)
        }
    }

    @objc private func countryTapped() {
        showCountryPicker()
    }

    @objc private func genderTapped() {
        showGenderPicker()
    }

    @objc private func saveTapped() {
        view.endEditing(true)

        // 调用保存接口
        saveUserData()
    }

    // MARK: - UIImagePickerControllerDelegate
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)

        // 优先使用编辑后的图片，如果没有则使用原图
        let image = info[.editedImage] as? UIImage ?? info[.originalImage] as? UIImage

        if let selectedImage = image {
            data.avatar = selectedImage
            avatarImageView.image = selectedImage

            // 注意：头像需要上传到服务器后才能更新 headFileName
            // 这里暂时不更新 LNUserManager 中的 headFileName
            // 实际项目中可能需要先上传图片，获得URL后再更新
        }
    }
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) { picker.dismiss(animated: true) }

    // MARK: - Keyboard handling
    private func setupKeyboardHandling() {
        NotificationCenter.default.addObserver(self, selector: #selector(kbChange(_:)), name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
        let tap = UITapGestureRecognizer(target: self, action: #selector(endEditing))
        tap.cancelsTouchesInView = false
        view.addGestureRecognizer(tap)
    }
    @objc private func endEditing() { view.endEditing(true) }

    @objc private func kbChange(_ note: Notification) {
        guard let userInfo = note.userInfo,
              let endFrame = (userInfo[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue,
              let duration = userInfo[UIResponder.keyboardAnimationDurationUserInfoKey] as? TimeInterval else { return }
        let insets = max(0, view.bounds.maxY - view.convert(endFrame, from: nil).minY)
        UIView.animate(withDuration: duration) {
            self.scrollView.contentInset.bottom = insets + 12
            self.scrollView.verticalScrollIndicatorInsets.bottom = insets
        }
    }

    // MARK: - Helpers
    private static func makeCard() -> UIView {
        let v = UIView()
        v.backgroundColor = UIColor.white
        v.layer.cornerRadius = 16
        v.layer.masksToBounds = true
        v.isUserInteractionEnabled = true // 确保可以接收用户交互
        return v
    }

    private static func makeTitleLabel(text: String) -> UILabel {
        let l = UILabel()
        l.text = text
        l.font = LNFont.forTextStyle(.body)
        l.textColor = UIColor.label
        l.adjustsFontForContentSizeCategory = true
        l.isUserInteractionEnabled = false // 确保不阻止父视图的点击事件
        return l
    }

    private static func makeValueLabel() -> UILabel {
        let l = UILabel()
        l.textAlignment = .right
        l.font = LNFont.forTextStyle(.body)
        l.textColor = UIColor.secondaryLabel
        l.adjustsFontForContentSizeCategory = true
        l.setContentCompressionResistancePriority(.required, for: .horizontal)
        l.setContentHuggingPriority(.defaultLow, for: .horizontal)
        l.isUserInteractionEnabled = false // 确保不阻止父视图的点击事件
        return l
    }

    private static func makeChevron() -> UIImageView {
        let iv = UIImageView(image: UIImage(systemName: "chevron.right"))
        iv.tintColor = UIColor.systemGray3
        iv.contentMode = .scaleAspectFit
        iv.isUserInteractionEnabled = false // 确保不阻止父视图的点击事件
        return iv
    }

    private static func makeTappableRow(target: Any, action: Selector) -> UIView {
        let v = UIView()
        v.isUserInteractionEnabled = true
        v.backgroundColor = UIColor.clear
        let tap = UITapGestureRecognizer(target: target, action: action)
        v.addGestureRecognizer(tap)
        return v
    }

    private static let dateFormatter: DateFormatter = {
        let f = DateFormatter()
        f.dateFormat = "yyyy-MM-dd"
        return f
    }()

    // MARK: - Network
    /// 获取用户详细信息
    private func fetchUserDetail() {
        guard let user = LNUserManager.shared.userModel else {
            Log("用户未登录，无法获取用户信息")
            bindDataToUI() // 使用默认数据
            return
        }

        let parameters = ["id": user.id]

        NetWorkRequest(LNApiProfile.userDetail(par: parameters)) { [weak self] result in
            guard let self = self else { return }

            Log("获取用户信息成功: \(result)")

            // 解析返回的用户数据
            if let data = result["data"] as? [String: Any] {
                self.updateUserDataFromAPI(data)
            } else {
                Log("用户信息数据格式错误")
                self.bindDataToUI() // 使用默认数据
            }

        } failure: { [weak self] error in
            guard let self = self else { return }

            Log("获取用户信息失败: \(error.localizedDescription)")

            // 网络请求失败，使用本地缓存数据
            self.bindDataToUI()

            // 可选：显示错误提示
            DispatchQueue.main.async {
                let alert = UIAlertController(
                    title: "提示",
                    message: "获取用户信息失败，显示本地数据",
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "确定", style: .default))
                self.present(alert, animated: true)
            }
        }
    }

    /// 根据API返回的数据更新用户信息
    private func updateUserDataFromAPI(_ apiData: [String: Any]) {
        // 更新姓名
        if let nickName = apiData["nickName"] as? String, !nickName.isEmpty {
            data.name = nickName
        }

        // 更新生日
        if let birthday = apiData["birthday"] as? String, !birthday.isEmpty {
            // 尝试解析生日字符串
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            if let birthDate = formatter.date(from: birthday) {
                data.birthDate = birthDate
            }
        }

        // 更新国家
        if let country = apiData["country"] as? String, !country.isEmpty {
            data.country = country
        }

        // 更新性别
        if let genderStr = apiData["gender"] as? String {
            switch genderStr {
            case "1":
                data.gender = .male
            case "2":
                data.gender = .female
            default:
                data.gender = .other
            }
        }

        // 更新头像（如果有头像URL）
        if let headFileName = apiData["headFileName"] as? String, !headFileName.isEmpty {
            loadAvatarFromURL(headFileName)
        }

        // 更新UI显示
        DispatchQueue.main.async {
            self.bindDataToUI()
        }
    }

    /// 从URL加载头像
    private func loadAvatarFromURL(_ urlString: String) {
        guard let url = URL(string: urlString) else { return }

        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let self = self,
                  let data = data,
                  let image = UIImage(data: data) else { return }

            DispatchQueue.main.async {
                self.data.avatar = image
                self.avatarImageView.image = image
            }
        }.resume()
    }

    /// 将用户模型转换为API参数字典
    private func convertUserModelToParameters(_ user: LNUserModel) -> [String: Any]? {
        // 使用 HandyJSON 将用户模型转为字典
        guard let jsonString = user.toJSONString(),
              let jsonData = jsonString.data(using: .utf8),
              let parameters = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any] else {
            return nil
        }

        return parameters
    }

    /// 保存用户数据到服务器
    private func saveUserData() {
        guard let user = LNUserManager.shared.userModel else {
            Log("用户未登录，无法保存用户信息")
            showSaveErrorAlert("用户未登录")
            return
        }

        // 将用户模型转为 JSON 字典
        guard let parameters = convertUserModelToParameters(user) else {
            Log("用户模型转换为参数失败")
            showSaveErrorAlert("数据格式错误")
            return
        }

        Log("准备保存的用户参数: \(parameters)")

        // 显示加载指示器
        showLoadingIndicator()

        NetWorkRequest(LNApiProfile.userUpdate(par: parameters)) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.hideLoadingIndicator()

                Log("保存用户信息成功: \(result)")

                // 更新用户token并保存
                self.updateUserTokenFromResponse(result)

                // 调用回调
                self.onSaved?(self.data)

                // 显示保存成功提示
                self.showSaveSuccessAlert()
            }

        } failure: { [weak self] error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.hideLoadingIndicator()

                Log("保存用户信息失败: \(error.localizedDescription)")

                // 显示错误提示
                self.showSaveErrorAlert(error.localizedDescription)
            }
        }
    }

    /// 从响应中更新用户token
    private func updateUserTokenFromResponse(_ response: [String: Any]) {
        guard let user = LNUserManager.shared.userModel else {
            Log("用户模型不存在，无法更新token")
            return
        }

        // 从响应中获取新的token
        if let newToken = response["data"] as? String {
            Log("更新用户token: \(newToken)")

            // 更新用户模型中的token
            user.token = newToken

            // 将更新后的用户数据持久化到本地
            LNUserManager.shared.saveUserInfoToDisk(user: user)

            Log("用户token更新并保存成功")
        } else {
            Log("响应中未找到token数据")

            // 即使没有新token，也要保存其他更新的用户信息
            LNUserManager.shared.saveUserInfoToDisk(user: user)
        }
    }

    /// 显示保存成功提示
    private func showSaveSuccessAlert() {
        let notice = "保存成功！\nName: \(data.name)\nDOB: \(Self.dateFormatter.string(from: data.birthDate))\nCountry: \(data.country)\nGender: \(data.gender.display)"
        let alert = UIAlertController(title: "保存成功", message: notice, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 显示保存失败提示
    private func showSaveErrorAlert(_ message: String) {
        let alert = UIAlertController(
            title: "保存失败",
            message: "保存用户信息时出现错误：\(message)",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 显示加载指示器
    private func showLoadingIndicator() {
        // 禁用保存按钮，防止重复提交
        saveButton.isEnabled = false
        saveButton.setTitle("保存中...", for: .normal)
    }

    /// 隐藏加载指示器
    private func hideLoadingIndicator() {
        // 恢复保存按钮
        saveButton.isEnabled = true
        saveButton.setTitle("save", for: .normal)
    }
}

