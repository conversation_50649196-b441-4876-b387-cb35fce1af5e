//
//  LNDoNotDisturbModal.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/14.
//

import UIKit
import SnapKit

/// Do Not Disturb 提醒弹窗
class LNDoNotDisturbModal: UIView {
    
    // MARK: - UI Elements
    private let backgroundOverlay: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()
    
    private let cardView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBackground
        view.layer.cornerRadius = s(16)
        view.layer.cornerCurve = .continuous
        view.clipsToBounds = true
        return view
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Remind"
        label.font = LNFont.medium(18)
        label.textColor = UIColor.label
        label.textAlignment = .center
        return label
    }()
    
    private let messageLabel: UILabel = {
        let label = UILabel()
        label.text = "When \"Do Not Disturb\" mode is on, you will not receive any calls."
        label.font = LNFont.regular(16)
        label.textColor = UIColor.secondaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var confirmButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Confirm", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.layer.cornerRadius = s(25)
        button.clipsToBounds = true
        return button
    }()
    
    private let confirmGradient: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = LNGradient.primaryColors
        layer.startPoint = LNGradient.primaryStartPoint
        layer.endPoint = LNGradient.primaryEndPoint
        return layer
    }()
    
    private let cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Cancel", for: .normal)
        button.setTitleColor(UIColor.label, for: .normal)
        button.titleLabel?.font = LNFont.regular(16)
        button.backgroundColor = UIColor.clear
        return button
    }()
    
    // MARK: - Properties
    var onConfirm: (() -> Void)?
    var onCancel: (() -> Void)?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        backgroundColor = UIColor.clear
        
        addSubview(backgroundOverlay)
        addSubview(cardView)
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(messageLabel)
        cardView.addSubview(confirmButton)
        cardView.addSubview(cancelButton)
        
    }
    
    private func setupConstraints() {
        backgroundOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        cardView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(s(40))
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(24))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        messageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(16))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        confirmButton.snp.makeConstraints { make in
            make.top.equalTo(messageLabel.snp.bottom).offset(s(32))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(50))
        }
        self.layoutIfNeeded()
        // 设置渐变背景
        confirmButton.layer.insertSublayer(confirmGradient, at: 0)
        
        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(confirmButton.snp.bottom).offset(s(16))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(44))
            make.bottom.equalToSuperview().offset(-s(24))
        }
    }
    
    private func setupActions() {
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        cancelButton.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
        
        // 点击背景遮罩关闭弹窗
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundOverlay.addGestureRecognizer(tapGesture)
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()

        // 直接设置渐变层的frame，不使用动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        confirmGradient.frame = confirmButton.bounds
        CATransaction.commit()
    }
    
    // MARK: - Actions
    @objc private func confirmTapped() {
        onConfirm?()
    }
    
    @objc private func cancelTapped() {
        onCancel?()
    }
    
    @objc private func backgroundTapped() {
        onCancel?()
    }
}

// MARK: - Public Methods
extension LNDoNotDisturbModal {
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 强制布局更新，确保渐变背景正确设置
        self.layoutIfNeeded()

        // 在动画开始前设置渐变背景，禁用隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        confirmGradient.frame = confirmButton.bounds
        if confirmGradient.superlayer == nil {
            confirmButton.layer.insertSublayer(confirmGradient, at: 0)
        }
        CATransaction.commit()

        // 显示动画
        alpha = 0
        cardView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)

        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.alpha = 1
            self.cardView.transform = .identity
        }
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
            self.cardView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}
