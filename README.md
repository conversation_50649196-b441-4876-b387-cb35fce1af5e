# LiveNow - iOS应用

## 项目概述
LiveNow是一个现代化的iOS应用，专注于社交和匹配功能。应用采用Tab Bar导航结构，为用户提供便捷的多功能体验。

## 功能特性
本应用包含四个主要功能模块：

### 1. 首页 (Home) 🏠
- 展示最新动态和推荐内容
- 用户可以浏览热门内容
- 提供搜索和发现功能

### 2. 匹配 (Match) 💝
- 智能匹配算法
- 用户可以查看匹配结果
- 支持筛选和偏好设置

### 3. 消息聊天 (Messages) 💬
- 实时聊天功能
- 消息列表和聊天详情
- 支持文字、图片等多媒体消息

### 4. 我的 (Profile) 👤
- 个人资料管理
- 设置和偏好配置
- 账户信息和隐私设置

#### 新增：个人信息数据页（LNPersonalDataViewController）
- 入口：我的 -> Personal Data
- 功能：头像选择、姓名编辑、生日选择、国家选择、性别选择、保存
- 技术：UIKit + SnapKit，支持 iOS 13，动态字体和深色模式
- 注意：如需从相册选择头像，请在 Info.plist 增加 NSPhotoLibraryUsageDescription（相册访问用途说明）


#### 新增：设置页（LNSettingViewController）
- 入口：我的 -> Setting
- 功能：免打扰开关、黑名单、帮助与反馈、删除账号、协议与关于、版本显示、清理缓存、退出登录
- 技术：UIKit + SnapKit；iOS 13 动态字体与深色模式

## 技术架构
- **开发语言**: Swift
- **UI框架**: UIKit
- **布局工具**: SnapKit ✅ (已集成使用)
- **响应式编程**: RxSwift (计划使用)
- **数据存储**: Core Data / SwiftData (计划使用)
- **设计规范**: 遵循 Apple Human Interface Guidelines
- **图标系统**: SF Symbols
- **命名规范**: 所有类使用LN前缀

## 项目结构
```
LiveNow/
├── AppDelegate.swift             # 应用委托
├── SceneDelegate.swift           # 场景委托
├── ViewControllers/             # 视图控制器（LN前缀命名）
│   ├── LNTabBarController.swift  # 主Tab控制器
│   ├── LNHomeViewController.swift # 首页
│   ├── LNMatchViewController.swift # 匹配页
│   ├── LNMessagesViewController.swift # 消息页
│   └── LNProfileViewController.swift # 个人页
├── Views/                       # 自定义视图
├── Models/                      # 数据模型
└── Resources/                  # 资源文件
    ├── Assets.xcassets         # 图片资源
    └── Base.lproj/            # 本地化文件
```

## 开发规范
- 使用Auto Layout和SnapKit进行界面布局
- 遵循MVC/MVVM架构模式
- 代码注释完整，可读性强
- 支持iPhone和iPad自适应布局
- 优化内存使用和性能

## 全局字体使用
本项目已集成自定义字体 HarmonyOS Sans（Regular/Medium/Bold/Black），并统一在代码层显式指定字体：

- 在 `Info.plist` 中通过 `UIAppFonts` 注册以下字体文件：
  - `HarmonyOS_Sans_Regular.ttf`
  - `HarmonyOS_Sans_Medium.ttf`
  - `HarmonyOS_Sans_Bold.ttf`
  - `HarmonyOS_Sans_Black.ttf`
- 在 `AppDelegate` 的 `application(_:didFinishLaunchingWithOptions:)` 中调用 `LNFontManager.shared.activate()` 完成外观设置（不做运行时递归替换）。
- 常用便捷方法（支持动态字体）：

```swift
// 固定字号
titleLabel.font = LNFont.medium(18)
bodyLabel.font = LNFont.regular(16)
strongLabel.font = LNFont.bold(16)

// 基于 TextStyle 的可缩放字体
let headline = LNFont.forTextStyle(.headline, weight: .medium)
let body = LNFont.forTextStyle(.body)
```

> 说明：项目不使用运行时遍历/方法交换来替换字体，所有字体均在代码中通过 `LNFont` 显式设置。

## 版本信息
- **当前版本**: 1.0.0
- **最低支持**: iOS 13.0+
- **开发工具**: Xcode 15+

## 开发进度
- [✅] 项目初始化
- [✅] Tab Bar结构搭建（使用LN前缀）
- [✅] 各页面功能实现（使用LN前缀）
- [✅] 界面美化和交互优化
- [✅] SnapKit约束库集成
- [✅] 统一命名规范（LN前缀）
- [⏳] 数据存储和网络请求
- [⏳] 高级功能实现

---
*LiveNow - 让生活更精彩* ✨